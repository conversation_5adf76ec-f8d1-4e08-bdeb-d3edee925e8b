// AI Provider Management Module
class AIProviderManager {
    constructor() {
        this.providers = {
            ollama: {
                name: 'Local Ollama',
                baseUrl: 'http://localhost:11434',
                requiresApiKey: false,
                models: [],
                endpoints: {
                    models: '/api/tags',
                    chat: '/api/chat'
                }
            },
            openai: {
                name: 'OpenAI',
                baseUrl: 'https://api.openai.com/v1',
                requiresApiKey: true,
                models: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'],
                endpoints: {
                    models: '/models',
                    chat: '/chat/completions'
                }
            },
            gemini: {
                name: 'Google Gemini',
                baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
                requiresApiKey: true,
                models: [
                    'gemini-2.0-flash-exp',
                    'gemini-1.5-flash',
                    'gemini-1.5-flash-8b',
                    'gemini-1.5-pro',
                    'gemini-1.0-pro',
                    'gemini-pro',
                    'gemini-pro-vision'
                ],
                endpoints: {
                    models: '/models',
                    chat: '/models/{model}:generateContent'
                }
            },
            deepseek: {
                name: 'DeepSeek',
                baseUrl: 'https://api.deepseek.com/v1',
                requiresApiKey: true,
                models: ['deepseek-chat', 'deepseek-coder'],
                endpoints: {
                    models: '/models',
                    chat: '/chat/completions'
                }
            },
            openrouter: {
                name: 'OpenRouter',
                baseUrl: 'https://openrouter.ai/api/v1',
                requiresApiKey: true,
                models: [], // Will be loaded dynamically
                endpoints: {
                    models: '/models',
                    chat: '/chat/completions'
                }
            }
        };
        
        this.currentProvider = 'ollama';
        this.currentModel = '';
    }

    async loadModelsForProvider(provider) {
        const providerConfig = this.providers[provider];
        if (!providerConfig) {
            console.error(`Unknown provider: ${provider}`);
            return [];
        }

        try {
            if (provider === 'ollama') {
                return await this.loadOllamaModels();
            } else if (provider === 'gemini') {
                return await this.loadGeminiModels();
            } else if (providerConfig.endpoints.models) {
                return await this.loadApiModels(provider);
            } else {
                return providerConfig.models;
            }
        } catch (error) {
            console.error(`Error loading models for ${provider}:`, error);
            return providerConfig.models || [];
        }
    }

    async loadOllamaModels() {
        try {
            const response = await fetch(`${this.providers.ollama.baseUrl}/api/tags`);
            const data = await response.json();
            return data.models.map(model => model.name);
        } catch (error) {
            console.error('Error loading Ollama models:', error);
            return [];
        }
    }

    async loadGeminiModels() {
        const apiKey = this.getApiKey('gemini');
        if (!apiKey) {
            console.warn('No API key found for Gemini, using default models');
            return this.providers.gemini.models;
        }

        try {
            const response = await fetch(`${this.providers.gemini.baseUrl}/models?key=${apiKey}`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            const models = data.models
                .filter(model => model.supportedGenerationMethods?.includes('generateContent'))
                .map(model => model.name.replace('models/', ''))
                .sort();

            return models.length > 0 ? models : this.providers.gemini.models;
        } catch (error) {
            console.error('Error loading Gemini models:', error);
            return this.providers.gemini.models;
        }
    }

    async loadApiModels(provider) {
        const providerConfig = this.providers[provider];
        const apiKey = this.getApiKey(provider);
        
        if (!apiKey) {
            console.warn(`No API key found for ${provider}`);
            return providerConfig.models;
        }

        try {
            const headers = this.getAuthHeaders(provider, apiKey);
            const response = await fetch(`${providerConfig.baseUrl}${providerConfig.endpoints.models}`, {
                headers
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            
            if (provider === 'openrouter') {
                return data.data.map(model => model.id);
            } else {
                return data.data.map(model => model.id);
            }
        } catch (error) {
            console.error(`Error loading ${provider} models:`, error);
            return providerConfig.models;
        }
    }

    getAuthHeaders(provider, apiKey) {
        const headers = {
            'Content-Type': 'application/json'
        };

        switch (provider) {
            case 'openai':
            case 'deepseek':
                headers['Authorization'] = `Bearer ${apiKey}`;
                break;
            case 'openrouter':
                headers['Authorization'] = `Bearer ${apiKey}`;
                headers['HTTP-Referer'] = window.location.origin;
                headers['X-Title'] = 'AI Chat Interface';
                break;
            case 'gemini':
                // Gemini uses API key as query parameter
                break;
        }

        return headers;
    }

    getApiKey(provider) {
        if (window.aiPersonaManager) {
            return window.aiPersonaManager.apiKeys[provider];
        }
        return null;
    }

    async testConnection(provider, apiKey) {
        const providerConfig = this.providers[provider];
        if (!providerConfig) {
            throw new Error(`Unknown provider: ${provider}`);
        }

        if (provider === 'ollama') {
            return await this.testOllamaConnection();
        } else if (provider === 'gemini') {
            return await this.testGeminiConnection(apiKey);
        } else {
            return await this.testOpenAICompatibleConnection(provider, apiKey);
        }
    }

    async testOllamaConnection() {
        try {
            const response = await fetch(`${this.providers.ollama.baseUrl}/api/tags`);
            return response.ok;
        } catch (error) {
            return false;
        }
    }

    async testGeminiConnection(apiKey) {
        if (!apiKey) return false;
        
        try {
            const response = await fetch(
                `${this.providers.gemini.baseUrl}/models?key=${apiKey}`
            );
            return response.ok;
        } catch (error) {
            return false;
        }
    }

    async testOpenAICompatibleConnection(provider, apiKey) {
        if (!apiKey) return false;
        
        const providerConfig = this.providers[provider];
        const headers = this.getAuthHeaders(provider, apiKey);
        
        try {
            const response = await fetch(`${providerConfig.baseUrl}${providerConfig.endpoints.models}`, {
                headers
            });
            return response.ok;
        } catch (error) {
            return false;
        }
    }

    async sendMessage(provider, model, messages, apiKey, onProgress = null, signal = null) {
        const providerConfig = this.providers[provider];
        if (!providerConfig) {
            throw new Error(`Unknown provider: ${provider}`);
        }

        if (provider === 'ollama') {
            return await this.sendOllamaMessage(model, messages, onProgress, signal);
        } else if (provider === 'gemini') {
            return await this.sendGeminiMessage(model, messages, apiKey, onProgress, signal);
        } else {
            return await this.sendOpenAICompatibleMessage(provider, model, messages, apiKey, onProgress, signal);
        }
    }

    async sendOllamaMessage(model, messages, onProgress, signal) {
        // Convert messages to Ollama format with image support
        const formattedMessages = this.convertMessagesToOllamaFormat(messages);

        const response = await fetch(`${this.providers.ollama.baseUrl}/api/chat`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model: model,
                messages: formattedMessages,
                stream: true,
                options: {
                    // Add timeout and performance options for image processing
                    timeout: 120000, // 2 minutes timeout for image processing
                    num_ctx: 4096
                }
            }),
            signal: signal
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return this.handleStreamResponse(response, onProgress);
    }

    convertMessagesToOllamaFormat(messages) {
        return messages.map(msg => {
            // Handle messages with images
            if (msg.images && msg.images.length > 0) {
                return {
                    role: msg.role,
                    content: msg.content,
                    images: msg.images // Ollama expects base64 images in the images array
                };
            }

            // Regular text message
            return {
                role: msg.role,
                content: msg.content
            };
        });
    }

    async sendOpenAICompatibleMessage(provider, model, messages, apiKey, onProgress, signal) {
        const providerConfig = this.providers[provider];
        const headers = this.getAuthHeaders(provider, apiKey);

        // Convert messages to OpenAI vision format if images are present
        const formattedMessages = this.convertMessagesToOpenAIFormat(messages);

        const response = await fetch(`${providerConfig.baseUrl}${providerConfig.endpoints.chat}`, {
            method: 'POST',
            headers,
            body: JSON.stringify({
                model: model,
                messages: formattedMessages,
                stream: true,
                max_tokens: 2048
            }),
            signal: signal
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return this.handleStreamResponse(response, onProgress, provider);
    }

    convertMessagesToOpenAIFormat(messages) {
        return messages.map(msg => {
            // Handle messages with images
            if (msg.images && msg.images.length > 0) {
                const content = [
                    {
                        type: "text",
                        text: msg.content
                    }
                ];

                // Add images to content array
                msg.images.forEach(imageBase64 => {
                    // Detect MIME type for proper data URL
                    let mimeType = "image/jpeg";
                    if (msg.imageType) {
                        mimeType = msg.imageType;
                    } else {
                        // Try to detect from base64 header
                        const base64Header = imageBase64.substring(0, 20);
                        if (base64Header.includes('PNG') || base64Header.startsWith('iVBOR')) {
                            mimeType = "image/png";
                        } else if (base64Header.includes('GIF') || base64Header.startsWith('R0lGOD')) {
                            mimeType = "image/gif";
                        } else if (base64Header.includes('WEBP') || base64Header.includes('UklGR')) {
                            mimeType = "image/webp";
                        }
                    }

                    content.push({
                        type: "image_url",
                        image_url: {
                            url: `data:${mimeType};base64,${imageBase64}`,
                            detail: "auto"
                        }
                    });
                });

                return {
                    role: msg.role,
                    content: content
                };
            }

            // Regular text message
            return {
                role: msg.role,
                content: msg.content
            };
        });
    }

    async sendGeminiMessage(model, messages, apiKey, onProgress, signal) {
        // Convert messages to Gemini format
        const contents = this.convertMessagesToGeminiFormat(messages);

        const url = `${this.providers.gemini.baseUrl}/models/${model}:streamGenerateContent?key=${apiKey}&alt=sse`;

        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                contents: contents,
                generationConfig: {
                    temperature: 0.7,
                    maxOutputTokens: 2048
                }
            }),
            signal: signal
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        // Debug log the raw response
        console.log('Gemini streaming response:', response);

        return this.handleStreamResponse(response, onProgress, 'gemini');
    }

    convertMessagesToGeminiFormat(messages) {
        return messages
            .filter(msg => msg.role !== 'system')
            .map(msg => {
                const parts = [{ text: msg.content }];

                // Add image support for Gemini
                if (msg.images && msg.images.length > 0) {
                    msg.images.forEach(imageBase64 => {
                        // Detect MIME type from image type or default to JPEG
                        let mimeType = "image/jpeg";
                        if (msg.imageType) {
                            mimeType = msg.imageType;
                        } else {
                            // Try to detect from base64 header if available
                            const base64Header = imageBase64.substring(0, 20);
                            if (base64Header.includes('PNG') || base64Header.startsWith('iVBOR')) {
                                mimeType = "image/png";
                            } else if (base64Header.includes('GIF') || base64Header.startsWith('R0lGOD')) {
                                mimeType = "image/gif";
                            } else if (base64Header.includes('WEBP') || base64Header.includes('UklGR')) {
                                mimeType = "image/webp";
                            }
                        }

                        parts.push({
                            inline_data: {
                                mime_type: mimeType,
                                data: imageBase64
                            }
                        });
                    });
                }

                return {
                    role: msg.role === 'assistant' ? 'model' : 'user',
                    parts: parts
                };
            });
    }

    async handleStreamResponse(response, onProgress, provider = 'ollama') {
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let fullResponse = '';
        let buffer = '';
        let lastProgressTime = Date.now();
        const progressTimeout = 30000; // 30 seconds timeout for progress

        try {
            while (true) {
                // Check for progress timeout (useful for image processing)
                if (Date.now() - lastProgressTime > progressTimeout) {
                    console.warn(`No progress for ${progressTimeout/1000}s, continuing...`);
                    lastProgressTime = Date.now();
                }

                const { done, value } = await reader.read();
                if (done) break;

                lastProgressTime = Date.now(); // Reset timeout on progress
                buffer += decoder.decode(value, { stream: true });
                const lines = buffer.split('\n');
                buffer = lines.pop(); // Keep incomplete line in buffer

                for (const line of lines) {
                    if (!line.trim()) continue;

                    try {
                        if (provider === 'ollama') {
                            const data = JSON.parse(line);
                            if (data.message?.content) {
                                fullResponse += data.message.content;
                                if (onProgress) onProgress(data.message.content);
                            }
                        } else if (provider === 'gemini') {
                            // Handle Gemini's SSE format
                            if (line.startsWith('data: ')) {
                                const jsonStr = line.slice(6).trim();
                                if (jsonStr === '[DONE]') continue;
                                
                                try {
                                    const data = JSON.parse(jsonStr);
                                    if (data.error) {
                                        console.error('Gemini API error:', data.error);
                                        continue;
                                    }
                                    
                                    // Handle both single and multiple candidates
                                    const candidates = Array.isArray(data.candidates) ? data.candidates : [data.candidates];
                                    for (const candidate of candidates) {
                                        if (!candidate) continue;
                                        
                                        // Handle both single and multiple parts
                                        const parts = Array.isArray(candidate.content?.parts) ? 
                                            candidate.content.parts : [candidate.content?.parts];
                                            
                                        for (const part of parts) {
                                            if (!part) continue;
                                            const content = part.text || '';
                                            if (content) {
                                                fullResponse += content;
                                                if (onProgress) onProgress(content);
                                            }
                                        }
                                    }
                                } catch (e) {
                                    console.warn('Error parsing Gemini chunk:', e, 'Raw:', line);
                                }
                            }
                        } else {
                            // OpenAI-compatible format
                            if (line.startsWith('data: ')) {
                                const jsonStr = line.slice(6);
                                if (jsonStr === '[DONE]') break;
                                
                                const data = JSON.parse(jsonStr);
                                const content = data.choices?.[0]?.delta?.content || '';
                                if (content) {
                                    fullResponse += content;
                                    if (onProgress) onProgress(content);
                                }
                            }
                        }
                    } catch (error) {
                        console.warn('Error parsing stream chunk:', error, 'Raw:', line);
                    }
                }
            }
        } finally {
            reader.releaseLock();
        }

        return fullResponse;
    }

    getProviderDisplayName(provider) {
        return this.providers[provider]?.name || provider;
    }

    requiresApiKey(provider) {
        return this.providers[provider]?.requiresApiKey || false;
    }

    getAvailableProviders() {
        return Object.keys(this.providers);
    }
}

// Initialize the AI Provider Manager
window.aiProviderManager = new AIProviderManager();
console.log('✅ AI Provider Manager initialized with providers:', Object.keys(window.aiProviderManager.providers));
