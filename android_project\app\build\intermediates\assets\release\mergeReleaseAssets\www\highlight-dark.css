/*

github.com style (c) <PERSON><PERSON> <<EMAIL>>

*/

.hljs {
  display: block;
  overflow-x: auto;
  padding: 0.5em;
  color: #f8f8f2;
  background: #282a36;
}

.hljs-comment,
.hljs-quote {
  color: #6272a4;
}

.hljs-deletion,
.hljs-name,
.hljs-regexp,
.hljs-selector-class,
.hljs-selector-id,
.hljs-tag,
.hljs-template-variable,
.hljs-variable {
  color: #ff5555;
}

.hljs-built_in,
.hljs-builtin-name,
.hljs-link,
.hljs-literal,
.hljs-meta,
.hljs-number,
.hljs-params,
.hljs-type {
  color: #bd93f9;
}

.hljs-attribute {
  color: #50fa7b;
}

.hljs-addition,
.hljs-bullet,
.hljs-string,
.hljs-symbol {
  color: #f1fa8c;
}

.hljs-section,
.hljs-title {
  color: #8be9fd;
}

.hljs-keyword,
.hljs-selector-tag {
  color: #ff79c6;
}

.hljs-emphasis {
  font-style: italic;
}

.hljs-strong {
  font-weight: bold;
}
