-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:50:9-58:20
	android:grantUriPermissions
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:54:13-47
	android:authorities
		INJECTED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:52:13-64
	android:exported
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:53:13-37
	android:name
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:51:13-62
manifest
ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:2:1-60:12
INJECTED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:2:1-60:12
INJECTED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:2:1-60:12
INJECTED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:2:1-60:12
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\55e63cee8861edff5d5ce5e2da6f9bdb\transformed\material-1.10.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f06089d2cd47e8f9edc08c6cd60dbb3\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\28e1adca9f6f8515b5e718b026885849\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c49fe9f01bdf72f5546a74fa012334ab\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.webkit:webkit:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5b3af991186285fdee2182667e7201b7\transformed\webkit-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a2eff5af8fe0a85d2d883ecbba71f4f4\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2132cab3fc2f3ad166fd8105439ebe05\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\d4e8918ec2c9009ee7b12e6b176e9b15\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1200ccb90779b05a64a5b4597886c4cc\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d8c9d120442cbab3110def35e297b96e\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\64e999c457a740eff668786494c619dc\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dbf48c7c857877162773fa4f91825078\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f7b8e47779c6d2f7605c52ba4228724f\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\74cf476dfcacf841fd7a8f4348f4fc5f\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2bbf287c24e35015668ed93bc13c2358\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\85e9b38c246a1334cfcc9ea75993f8fa\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5cd9fa06760422efb2051170684b3958\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b5cfa8ee1f7fd43e0cd3b6f214b06d48\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5625dc4d28d5d78e6efcb47eaf965e3c\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\fee7e872745977873d770daad3d1b5f9\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\002069901257091e303b084a0bc88eae\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\123aae8284082ba575e866108ef500bb\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc997b3b39ab4b8760928d7e13a2d961\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\372a1659378af44e8b6a5661bf48c0eb\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f81e34e37e3dcd0eb8b25b1e3f16cb8\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7f0ee2e89267ef62485753e38587c51b\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c4c97640dd76364d34c81736a88d160e\transformed\core-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ac94add5d222ea701edfe5ced2873bb5\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b57a5c9c70a47ee91c47061360979e05\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e2f996ea1a0dab32d27d6ab56dd6f729\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\40788280d6c9ba383cd785ca86186cd1\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cb771da7948c6afe502ef472e547fdba\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bd49aaf90a0115dcd83a0d92d807edda\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\830ede1bd10b81d76236c616f4a02ae7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a88aabd64f3ba570de890ad1aff22de5\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2a405fae2804ecd13c769da934c2df76\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2108596cb74e98170436e6cf94048ffa\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8336b31fbb1607eb52dd9b264316461d\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bda96040413622a9ca7115e351e6d96d\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b644fcca9d53e6b64458c7c5b6a5fb7\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\38c60e057031aa31bc2690fbd2bb6f49\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:6:5-67
	android:name
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:7:5-79
	android:name
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.CAMERA
ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:10:5-65
	android:name
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:10:22-62
uses-feature#android.hardware.camera
ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:11:5-85
	android:required
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:11:58-82
	android:name
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:11:19-57
uses-feature#android.hardware.camera.autofocus
ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:12:5-95
	android:required
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:12:68-92
	android:name
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:12:19-67
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:15:5-80
	android:name
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:15:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:16:5-17:38
	android:maxSdkVersion
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:17:9-35
	android:name
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:16:22-78
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:18:5-76
	android:name
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:18:22-73
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:19:5-75
	android:name
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:19:22-72
uses-permission#android.permission.READ_MEDIA_AUDIO
ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:20:5-75
	android:name
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:20:22-72
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:23:5-24:40
	tools:ignore
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:24:9-37
	android:name
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:23:22-79
application
ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:26:5-59:19
INJECTED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:26:5-59:19
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\55e63cee8861edff5d5ce5e2da6f9bdb\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\55e63cee8861edff5d5ce5e2da6f9bdb\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f06089d2cd47e8f9edc08c6cd60dbb3\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f06089d2cd47e8f9edc08c6cd60dbb3\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\64e999c457a740eff668786494c619dc\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\64e999c457a740eff668786494c619dc\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\002069901257091e303b084a0bc88eae\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\002069901257091e303b084a0bc88eae\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bd49aaf90a0115dcd83a0d92d807edda\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bd49aaf90a0115dcd83a0d92d807edda\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\830ede1bd10b81d76236c616f4a02ae7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\830ede1bd10b81d76236c616f4a02ae7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
	android:label
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:31:9-41
	android:fullBackupContent
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:29:9-54
	android:roundIcon
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:32:9-54
	tools:targetApi
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:35:9-29
	android:icon
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:30:9-43
	android:allowBackup
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:27:9-35
	android:theme
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:33:9-58
	android:dataExtractionRules
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:28:9-65
	android:usesCleartextTraffic
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:34:9-44
activity#com.jermescode.ai.MainActivity
ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:37:9-47:20
	android:screenOrientation
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:41:13-52
	android:exported
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:39:13-36
	android:configChanges
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:42:13-74
	android:theme
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:40:13-62
	android:name
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:38:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:43:13-46:29
action#android.intent.action.MAIN
ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:44:17-69
	android:name
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:44:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:45:17-77
	android:name
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:45:27-74
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:55:13-57:54
	android:resource
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:57:17-51
	android:name
		ADDED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:56:17-67
uses-sdk
INJECTED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml
INJECTED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\55e63cee8861edff5d5ce5e2da6f9bdb\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\55e63cee8861edff5d5ce5e2da6f9bdb\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f06089d2cd47e8f9edc08c6cd60dbb3\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f06089d2cd47e8f9edc08c6cd60dbb3\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\28e1adca9f6f8515b5e718b026885849\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\28e1adca9f6f8515b5e718b026885849\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c49fe9f01bdf72f5546a74fa012334ab\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c49fe9f01bdf72f5546a74fa012334ab\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.webkit:webkit:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5b3af991186285fdee2182667e7201b7\transformed\webkit-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5b3af991186285fdee2182667e7201b7\transformed\webkit-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a2eff5af8fe0a85d2d883ecbba71f4f4\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a2eff5af8fe0a85d2d883ecbba71f4f4\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2132cab3fc2f3ad166fd8105439ebe05\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2132cab3fc2f3ad166fd8105439ebe05\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\d4e8918ec2c9009ee7b12e6b176e9b15\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\d4e8918ec2c9009ee7b12e6b176e9b15\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1200ccb90779b05a64a5b4597886c4cc\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1200ccb90779b05a64a5b4597886c4cc\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d8c9d120442cbab3110def35e297b96e\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d8c9d120442cbab3110def35e297b96e\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\64e999c457a740eff668786494c619dc\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\64e999c457a740eff668786494c619dc\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dbf48c7c857877162773fa4f91825078\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dbf48c7c857877162773fa4f91825078\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f7b8e47779c6d2f7605c52ba4228724f\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f7b8e47779c6d2f7605c52ba4228724f\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\74cf476dfcacf841fd7a8f4348f4fc5f\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\74cf476dfcacf841fd7a8f4348f4fc5f\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2bbf287c24e35015668ed93bc13c2358\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2bbf287c24e35015668ed93bc13c2358\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\85e9b38c246a1334cfcc9ea75993f8fa\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\85e9b38c246a1334cfcc9ea75993f8fa\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5cd9fa06760422efb2051170684b3958\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5cd9fa06760422efb2051170684b3958\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b5cfa8ee1f7fd43e0cd3b6f214b06d48\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b5cfa8ee1f7fd43e0cd3b6f214b06d48\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5625dc4d28d5d78e6efcb47eaf965e3c\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5625dc4d28d5d78e6efcb47eaf965e3c\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\fee7e872745977873d770daad3d1b5f9\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\fee7e872745977873d770daad3d1b5f9\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\002069901257091e303b084a0bc88eae\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\002069901257091e303b084a0bc88eae\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\123aae8284082ba575e866108ef500bb\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\123aae8284082ba575e866108ef500bb\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc997b3b39ab4b8760928d7e13a2d961\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc997b3b39ab4b8760928d7e13a2d961\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\372a1659378af44e8b6a5661bf48c0eb\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\372a1659378af44e8b6a5661bf48c0eb\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f81e34e37e3dcd0eb8b25b1e3f16cb8\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f81e34e37e3dcd0eb8b25b1e3f16cb8\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7f0ee2e89267ef62485753e38587c51b\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7f0ee2e89267ef62485753e38587c51b\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c4c97640dd76364d34c81736a88d160e\transformed\core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c4c97640dd76364d34c81736a88d160e\transformed\core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ac94add5d222ea701edfe5ced2873bb5\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ac94add5d222ea701edfe5ced2873bb5\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b57a5c9c70a47ee91c47061360979e05\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b57a5c9c70a47ee91c47061360979e05\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e2f996ea1a0dab32d27d6ab56dd6f729\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e2f996ea1a0dab32d27d6ab56dd6f729\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\40788280d6c9ba383cd785ca86186cd1\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\40788280d6c9ba383cd785ca86186cd1\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cb771da7948c6afe502ef472e547fdba\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cb771da7948c6afe502ef472e547fdba\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bd49aaf90a0115dcd83a0d92d807edda\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bd49aaf90a0115dcd83a0d92d807edda\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\830ede1bd10b81d76236c616f4a02ae7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\830ede1bd10b81d76236c616f4a02ae7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a88aabd64f3ba570de890ad1aff22de5\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a88aabd64f3ba570de890ad1aff22de5\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2a405fae2804ecd13c769da934c2df76\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2a405fae2804ecd13c769da934c2df76\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2108596cb74e98170436e6cf94048ffa\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2108596cb74e98170436e6cf94048ffa\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8336b31fbb1607eb52dd9b264316461d\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8336b31fbb1607eb52dd9b264316461d\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bda96040413622a9ca7115e351e6d96d\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bda96040413622a9ca7115e351e6d96d\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b644fcca9d53e6b64458c7c5b6a5fb7\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b644fcca9d53e6b64458c7c5b6a5fb7\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\38c60e057031aa31bc2690fbd2bb6f49\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\38c60e057031aa31bc2690fbd2bb6f49\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\64e999c457a740eff668786494c619dc\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\002069901257091e303b084a0bc88eae\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\002069901257091e303b084a0bc88eae\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\830ede1bd10b81d76236c616f4a02ae7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\830ede1bd10b81d76236c616f4a02ae7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\64e999c457a740eff668786494c619dc\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\64e999c457a740eff668786494c619dc\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\64e999c457a740eff668786494c619dc\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\64e999c457a740eff668786494c619dc\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\64e999c457a740eff668786494c619dc\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\64e999c457a740eff668786494c619dc\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\64e999c457a740eff668786494c619dc\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\002069901257091e303b084a0bc88eae\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\002069901257091e303b084a0bc88eae\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\002069901257091e303b084a0bc88eae\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
permission#com.jermescode.ai.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
uses-permission#com.jermescode.ai.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
