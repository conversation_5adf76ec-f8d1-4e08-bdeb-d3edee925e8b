{"logs": [{"outputFile": "com.jermescode.ai.app-mergeReleaseResources-31:/values-kk/values-kk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\55e63cee8861edff5d5ce5e2da6f9bdb\\transformed\\material-1.10.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,351,427,506,600,688,780,892,974,1038,1133,1203,1266,1373,1438,1505,1566,1633,1695,1749,1863,1922,1983,2037,2112,2238,2326,2416,2558,2630,2703,2840,2929,3010,3067,3123,3189,3260,3337,3423,3503,3575,3651,3732,3802,3902,3989,4061,4152,4245,4319,4394,4486,4538,4620,4686,4770,4856,4918,4982,5045,5114,5218,5322,5416,5516,5577,5637", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,77,75,78,93,87,91,111,81,63,94,69,62,106,64,66,60,66,61,53,113,58,60,53,74,125,87,89,141,71,72,136,88,80,56,55,65,70,76,85,79,71,75,80,69,99,86,71,90,92,73,74,91,51,81,65,83,85,61,63,62,68,103,103,93,99,60,59,83", "endOffsets": "268,346,422,501,595,683,775,887,969,1033,1128,1198,1261,1368,1433,1500,1561,1628,1690,1744,1858,1917,1978,2032,2107,2233,2321,2411,2553,2625,2698,2835,2924,3005,3062,3118,3184,3255,3332,3418,3498,3570,3646,3727,3797,3897,3984,4056,4147,4240,4314,4389,4481,4533,4615,4681,4765,4851,4913,4977,5040,5109,5213,5317,5411,5511,5572,5632,5716"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3014,3092,3168,3247,3341,3429,3521,3633,3715,3779,3874,3944,4007,4114,4179,4246,4307,4374,4436,4490,4604,4663,4724,4778,4853,4979,5067,5157,5299,5371,5444,5581,5670,5751,5808,5864,5930,6001,6078,6164,6244,6316,6392,6473,6543,6643,6730,6802,6893,6986,7060,7135,7227,7279,7361,7427,7511,7597,7659,7723,7786,7855,7959,8063,8157,8257,8318,8378", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100", "endColumns": "12,77,75,78,93,87,91,111,81,63,94,69,62,106,64,66,60,66,61,53,113,58,60,53,74,125,87,89,141,71,72,136,88,80,56,55,65,70,76,85,79,71,75,80,69,99,86,71,90,92,73,74,91,51,81,65,83,85,61,63,62,68,103,103,93,99,60,59,83", "endOffsets": "318,3087,3163,3242,3336,3424,3516,3628,3710,3774,3869,3939,4002,4109,4174,4241,4302,4369,4431,4485,4599,4658,4719,4773,4848,4974,5062,5152,5294,5366,5439,5576,5665,5746,5803,5859,5925,5996,6073,6159,6239,6311,6387,6468,6538,6638,6725,6797,6888,6981,7055,7130,7222,7274,7356,7422,7506,7592,7654,7718,7781,7850,7954,8058,8152,8252,8313,8373,8457"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\6325cad999e7e25eaa9301d33937688b\\transformed\\core-1.9.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "102", "startColumns": "4", "startOffsets": "8544", "endColumns": "100", "endOffsets": "8640"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c49fe9f01bdf72f5546a74fa012334ab\\transformed\\appcompat-1.6.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,428,513,619,738,818,895,986,1079,1174,1268,1368,1461,1556,1653,1744,1835,1916,2021,2124,2222,2329,2435,2535,2701,2796", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "208,313,423,508,614,733,813,890,981,1074,1169,1263,1363,1456,1551,1648,1739,1830,1911,2016,2119,2217,2324,2430,2530,2696,2791,2873"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,431,536,646,731,837,956,1036,1113,1204,1297,1392,1486,1586,1679,1774,1871,1962,2053,2134,2239,2342,2440,2547,2653,2753,2919,8462", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "426,531,641,726,832,951,1031,1108,1199,1292,1387,1481,1581,1674,1769,1866,1957,2048,2129,2234,2337,2435,2542,2648,2748,2914,3009,8539"}}]}]}