<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app_dev\JermesCode_Ai\android_project\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app_dev\JermesCode_Ai\android_project\app\src\main\res"><file name="activity_main" path="D:\app_dev\JermesCode_Ai\android_project\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="D:\app_dev\JermesCode_Ai\android_project\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\app_dev\JermesCode_Ai\android_project\app\src\main\res\mipmap-hdpi\ic_launcher_round.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\app_dev\JermesCode_Ai\android_project\app\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\app_dev\JermesCode_Ai\android_project\app\src\main\res\mipmap-xhdpi\ic_launcher_round.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\app_dev\JermesCode_Ai\android_project\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\app_dev\JermesCode_Ai\android_project\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\app_dev\JermesCode_Ai\android_project\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\app_dev\JermesCode_Ai\android_project\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\app_dev\JermesCode_Ai\android_project\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="primary_color">#2196F3</color><color name="primary_dark">#1976D2</color><color name="accent_color">#FF4081</color><color name="background_color">#FAFAFA</color><color name="surface_color">#FFFFFF</color><color name="error_color">#F44336</color><color name="splash_background">#1E1E1E</color></file><file path="D:\app_dev\JermesCode_Ai\android_project\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">JermesaCode Ai</string><string name="loading">Loading...</string><string name="error_loading">Error loading page</string><string name="permission_camera_rationale">Camera permission is needed to capture photos for the AI chat.</string><string name="permission_storage_rationale">Storage permission is needed to save and load chat history.</string><string name="permission_denied">Permission denied. Some features may not work properly.</string><string name="download_started">Download started</string><string name="file_upload_error">Error uploading file</string></file><file path="D:\app_dev\JermesCode_Ai\android_project\app\src\main\res\values\themes.xml" qualifiers=""><style name="Base.Theme.JermesaCodeAi" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/accent_color</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="android:colorBackground">@color/background_color</item>
        <item name="colorSurface">@color/surface_color</item>
        <item name="colorError">@color/error_color</item>
        <item name="android:statusBarColor">@color/primary_dark</item>
        <item name="android:navigationBarColor">@color/primary_dark</item>
        <item name="android:windowLightStatusBar">false</item>
    </style><style name="Theme.JermesaCodeAi" parent="Base.Theme.JermesaCodeAi"/><style name="Theme.JermesaCodeAi.Splash" parent="Theme.SplashScreen">
        <item name="windowSplashScreenBackground">@color/splash_background</item>
        <item name="windowSplashScreenAnimatedIcon">@mipmap/ic_launcher</item>
        <item name="windowSplashScreenAnimationDuration">1000</item>
        <item name="postSplashScreenTheme">@style/Theme.JermesaCodeAi</item>
    </style></file><file name="backup_rules" path="D:\app_dev\JermesCode_Ai\android_project\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\app_dev\JermesCode_Ai\android_project\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="file_paths" path="D:\app_dev\JermesCode_Ai\android_project\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app_dev\JermesCode_Ai\android_project\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app_dev\JermesCode_Ai\android_project\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app_dev\JermesCode_Ai\android_project\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app_dev\JermesCode_Ai\android_project\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>