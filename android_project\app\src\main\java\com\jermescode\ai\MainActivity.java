package com.jermescode.ai;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.DownloadManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.provider.MediaStore;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.webkit.ConsoleMessage;
import android.webkit.DownloadListener;
import android.webkit.GeolocationPermissions;
import android.webkit.JavascriptInterface;
import android.webkit.PermissionRequest;
import android.webkit.ValueCallback;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ProgressBar;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.content.FileProvider;
import androidx.core.splashscreen.SplashScreen;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.Map;
import java.util.HashMap;

import org.json.JSONObject;
import org.json.JSONException;

public class MainActivity extends AppCompatActivity {
    private static final String TAG = "MainActivity";
    private static final int REQUEST_CAMERA_PERMISSION = 1001;
    private static final int REQUEST_STORAGE_PERMISSION = 1002;
    private static final int REQUEST_FILE_PICKER = 1003;
    private static final int REQUEST_CAMERA_CAPTURE = 1004;
    
    private WebView webView;
    private ProgressBar progressBar;
    private ValueCallback<Uri[]> fileUploadCallback;
    private String cameraPhotoPath;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        // Handle the splash screen transition
        SplashScreen splashScreen = SplashScreen.installSplashScreen(this);
        
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        initializeViews();
        setupWebView();
        requestPermissions();
        
        // Load the web application
        webView.loadUrl("file:///android_asset/www/index.html");
    }

    private void initializeViews() {
        webView = findViewById(R.id.webview);
        progressBar = findViewById(R.id.progress_bar);
    }

    @SuppressLint("SetJavaScriptEnabled")
    private void setupWebView() {
        WebSettings webSettings = webView.getSettings();
        
        // Enable JavaScript
        webSettings.setJavaScriptEnabled(true);
        webSettings.setJavaScriptCanOpenWindowsAutomatically(true);
        
        // Enable DOM storage
        webSettings.setDomStorageEnabled(true);
        webSettings.setDatabaseEnabled(true);
        
        // Enable file access
        webSettings.setAllowFileAccess(true);
        webSettings.setAllowContentAccess(true);
        webSettings.setAllowFileAccessFromFileURLs(true);
        webSettings.setAllowUniversalAccessFromFileURLs(true);
        
        // Enable mixed content
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            webSettings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        }
        
        // Set user agent
        webSettings.setUserAgentString(webSettings.getUserAgentString() + " JermesaCodeAi/1.0");
        
        // Enable zoom controls
        webSettings.setSupportZoom(true);
        webSettings.setBuiltInZoomControls(true);
        webSettings.setDisplayZoomControls(false);
        
        // Enable caching
        webSettings.setCacheMode(WebSettings.LOAD_DEFAULT);
        //webSettings.setAppCacheEnabled(true);
        
        // Set WebView client
        webView.setWebViewClient(new CustomWebViewClient());
        webView.setWebChromeClient(new CustomWebChromeClient());

        // Add JavaScript interface for API calls
        webView.addJavascriptInterface(new ApiInterface(), "AndroidAPI");

        // Set download listener
        webView.setDownloadListener(new CustomDownloadListener());
    }

    private void requestPermissions() {
        String[] permissions = {
            Manifest.permission.CAMERA,
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
        };
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            permissions = new String[]{
                Manifest.permission.CAMERA,
                Manifest.permission.READ_MEDIA_IMAGES,
                Manifest.permission.READ_MEDIA_VIDEO,
                Manifest.permission.READ_MEDIA_AUDIO
            };
        }
        
        boolean needsPermission = false;
        for (String permission : permissions) {
            if (ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
                needsPermission = true;
                break;
            }
        }
        
        if (needsPermission) {
            ActivityCompat.requestPermissions(this, permissions, REQUEST_STORAGE_PERMISSION);
        }
    }

    private class CustomWebViewClient extends WebViewClient {
        @Override
        public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
            String url = request.getUrl().toString();

            // Handle external URLs
            if (url.startsWith("http://") || url.startsWith("https://")) {
                if (!url.contains("localhost") && !url.contains("127.0.0.1")) {
                    // Open external URLs in browser
                    Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
                    startActivity(intent);
                    return true;
                }
            }

            return false;
        }

        @Override
        public void onPageStarted(WebView view, String url, android.graphics.Bitmap favicon) {
            super.onPageStarted(view, url, favicon);
            progressBar.setVisibility(View.VISIBLE);
        }

        @Override
        public void onPageFinished(WebView view, String url) {
            super.onPageFinished(view, url);
            progressBar.setVisibility(View.GONE);
        }
    }

    private class CustomWebChromeClient extends WebChromeClient {
        @Override
        public boolean onConsoleMessage(ConsoleMessage consoleMessage) {
            Log.d(TAG, "Console: " + consoleMessage.message() + " -- From line "
                    + consoleMessage.lineNumber() + " of " + consoleMessage.sourceId());
            return true;
        }

        @Override
        public void onProgressChanged(WebView view, int newProgress) {
            super.onProgressChanged(view, newProgress);
            progressBar.setProgress(newProgress);
        }

        @Override
        public boolean onShowFileChooser(WebView webView, ValueCallback<Uri[]> filePathCallback,
                                       FileChooserParams fileChooserParams) {
            fileUploadCallback = filePathCallback;

            Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
            intent.addCategory(Intent.CATEGORY_OPENABLE);
            intent.setType("*/*");

            // Create camera intent
            Intent cameraIntent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
            if (cameraIntent.resolveActivity(getPackageManager()) != null) {
                File photoFile = null;
                try {
                    photoFile = createImageFile();
                } catch (IOException ex) {
                    Log.e(TAG, "Error occurred while creating the File", ex);
                }

                if (photoFile != null) {
                    cameraPhotoPath = "file:" + photoFile.getAbsolutePath();
                    Uri photoURI = FileProvider.getUriForFile(MainActivity.this,
                            "com.jermescode.ai.fileprovider", photoFile);
                    cameraIntent.putExtra(MediaStore.EXTRA_OUTPUT, photoURI);
                }
            }

            // Create chooser intent
            Intent chooserIntent = Intent.createChooser(intent, "Select File");
            chooserIntent.putExtra(Intent.EXTRA_INITIAL_INTENTS, new Intent[]{cameraIntent});

            startActivityForResult(chooserIntent, REQUEST_FILE_PICKER);
            return true;
        }

        @Override
        public void onPermissionRequest(PermissionRequest request) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                request.grant(request.getResources());
            }
        }

        @Override
        public void onGeolocationPermissionsShowPrompt(String origin,
                                                      GeolocationPermissions.Callback callback) {
            callback.invoke(origin, true, false);
        }
    }

    private class CustomDownloadListener implements DownloadListener {
        @Override
        public void onDownloadStart(String url, String userAgent, String contentDisposition,
                                  String mimeType, long contentLength) {
            DownloadManager.Request request = new DownloadManager.Request(Uri.parse(url));
            request.setMimeType(mimeType);

            String filename = "download";
            if (contentDisposition != null && contentDisposition.contains("filename=")) {
                filename = contentDisposition.substring(contentDisposition.indexOf("filename=") + 9);
                filename = filename.replaceAll("\"", "");
            }

            request.setDestinationInExternalPublicDir(Environment.DIRECTORY_DOWNLOADS, filename);
            request.setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED);
            request.setVisibleInDownloadsUi(true);

            DownloadManager downloadManager = (DownloadManager) getSystemService(Context.DOWNLOAD_SERVICE);
            downloadManager.enqueue(request);

            Toast.makeText(MainActivity.this, "Downloading " + filename, Toast.LENGTH_SHORT).show();
        }
    }

    private File createImageFile() throws IOException {
        String timeStamp = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(new Date());
        String imageFileName = "JPEG_" + timeStamp + "_";
        File storageDir = getExternalFilesDir(Environment.DIRECTORY_PICTURES);
        return File.createTempFile(imageFileName, ".jpg", storageDir);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == REQUEST_FILE_PICKER) {
            if (fileUploadCallback == null) return;

            Uri[] results = null;

            if (resultCode == Activity.RESULT_OK) {
                if (data == null) {
                    // Camera capture
                    if (cameraPhotoPath != null) {
                        results = new Uri[]{Uri.parse(cameraPhotoPath)};
                    }
                } else {
                    // File picker
                    String dataString = data.getDataString();
                    if (dataString != null) {
                        results = new Uri[]{Uri.parse(dataString)};
                    }
                }
            }

            fileUploadCallback.onReceiveValue(results);
            fileUploadCallback = null;
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
                                         @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        if (requestCode == REQUEST_STORAGE_PERMISSION || requestCode == REQUEST_CAMERA_PERMISSION) {
            boolean allGranted = true;
            for (int result : grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    allGranted = false;
                    break;
                }
            }

            if (!allGranted) {
                Toast.makeText(this, "Some permissions were denied. App functionality may be limited.",
                        Toast.LENGTH_LONG).show();
            }
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && webView.canGoBack()) {
            webView.goBack();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    protected void onDestroy() {
        if (webView != null) {
            webView.destroy();
        }
        super.onDestroy();
    }

    /**
     * JavaScript Interface for API calls
     * This allows the WebView to make HTTP requests through native Android code,
     * bypassing CORS restrictions that prevent direct API calls from file:// origins
     */
    public class ApiInterface {

        @JavascriptInterface
        public void makeApiCall(String url, String method, String headers, String body, String callbackId) {
            new Thread(() -> {
                try {
                    String result = performHttpRequest(url, method, headers, body);

                    // Return result to JavaScript
                    runOnUiThread(() -> {
                        String jsCallback = String.format(
                            "if (window.apiCallbacks && window.apiCallbacks['%s']) { " +
                            "window.apiCallbacks['%s'](%s); " +
                            "delete window.apiCallbacks['%s']; }",
                            callbackId, callbackId, result, callbackId
                        );
                        webView.evaluateJavascript(jsCallback, null);
                    });

                } catch (Exception e) {
                    Log.e(TAG, "API call failed", e);

                    // Return error to JavaScript
                    runOnUiThread(() -> {
                        String errorResult = String.format(
                            "{\"success\": false, \"error\": \"%s\"}",
                            e.getMessage().replace("\"", "\\\"")
                        );
                        String jsCallback = String.format(
                            "if (window.apiCallbacks && window.apiCallbacks['%s']) { " +
                            "window.apiCallbacks['%s'](%s); " +
                            "delete window.apiCallbacks['%s']; }",
                            callbackId, callbackId, errorResult, callbackId
                        );
                        webView.evaluateJavascript(jsCallback, null);
                    });
                }
            }).start();
        }

        @JavascriptInterface
        public void makeStreamingApiCall(String url, String method, String headers, String body, String callbackId) {
            new Thread(() -> {
                try {
                    performStreamingHttpRequest(url, method, headers, body, callbackId);
                } catch (Exception e) {
                    Log.e(TAG, "Streaming API call failed", e);

                    // Return error to JavaScript
                    runOnUiThread(() -> {
                        String errorResult = String.format(
                            "{\"success\": false, \"error\": \"%s\"}",
                            e.getMessage().replace("\"", "\\\"")
                        );
                        String jsCallback = String.format(
                            "if (window.streamCallbacks && window.streamCallbacks['%s']) { " +
                            "window.streamCallbacks['%s'](%s); " +
                            "delete window.streamCallbacks['%s']; }",
                            callbackId, callbackId, errorResult, callbackId
                        );
                        webView.evaluateJavascript(jsCallback, null);
                    });
                }
            }).start();
        }
    }

    /**
     * Perform HTTP request with proper headers and error handling
     */
    private String performHttpRequest(String urlString, String method, String headersJson, String body) throws Exception {
        URL url = new URL(urlString);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        try {
            connection.setRequestMethod(method);
            connection.setConnectTimeout(30000); // 30 seconds
            connection.setReadTimeout(60000); // 60 seconds

            // Set headers
            if (headersJson != null && !headersJson.isEmpty()) {
                try {
                    JSONObject headers = new JSONObject(headersJson);
                    headers.keys().forEachRemaining(key -> {
                        String value = headers.optString(key);
                        if (value != null && !value.isEmpty()) {
                            connection.setRequestProperty(key, value);
                        }
                    });
                } catch (JSONException e) {
                    Log.w(TAG, "Failed to parse headers JSON", e);
                }
            }

            // Set default headers
            connection.setRequestProperty("User-Agent", "JermesaCodeAi/1.0 (Android)");

            // Send body if present
            if (body != null && !body.isEmpty() && ("POST".equals(method) || "PUT".equals(method))) {
                connection.setDoOutput(true);
                try (OutputStream os = connection.getOutputStream()) {
                    os.write(body.getBytes("UTF-8"));
                    os.flush();
                }
            }

            // Get response
            int responseCode = connection.getResponseCode();
            InputStream inputStream = responseCode >= 200 && responseCode < 300
                ? connection.getInputStream()
                : connection.getErrorStream();

            StringBuilder response = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line).append("\n");
                }
            }

            // Create result JSON
            JSONObject result = new JSONObject();
            result.put("success", responseCode >= 200 && responseCode < 300);
            result.put("status", responseCode);
            result.put("data", response.toString().trim());

            return result.toString();

        } finally {
            connection.disconnect();
        }
    }

    /**
     * Perform streaming HTTP request for real-time AI responses
     */
    private void performStreamingHttpRequest(String urlString, String method, String headersJson, String body, String callbackId) throws Exception {
        URL url = new URL(urlString);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        try {
            connection.setRequestMethod(method);
            connection.setConnectTimeout(30000); // 30 seconds
            connection.setReadTimeout(0); // No timeout for streaming

            // Set headers
            if (headersJson != null && !headersJson.isEmpty()) {
                try {
                    JSONObject headers = new JSONObject(headersJson);
                    headers.keys().forEachRemaining(key -> {
                        String value = headers.optString(key);
                        if (value != null && !value.isEmpty()) {
                            connection.setRequestProperty(key, value);
                        }
                    });
                } catch (JSONException e) {
                    Log.w(TAG, "Failed to parse headers JSON", e);
                }
            }

            // Set default headers
            connection.setRequestProperty("User-Agent", "JermesaCodeAi/1.0 (Android)");

            // Send body if present
            if (body != null && !body.isEmpty() && ("POST".equals(method) || "PUT".equals(method))) {
                connection.setDoOutput(true);
                try (OutputStream os = connection.getOutputStream()) {
                    os.write(body.getBytes("UTF-8"));
                    os.flush();
                }
            }

            // Get response
            int responseCode = connection.getResponseCode();
            if (responseCode >= 200 && responseCode < 300) {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        final String chunk = line;

                        // Send each chunk to JavaScript
                        runOnUiThread(() -> {
                            try {
                                JSONObject chunkResult = new JSONObject();
                                chunkResult.put("success", true);
                                chunkResult.put("chunk", chunk);
                                chunkResult.put("done", false);

                                String jsCallback = String.format(
                                    "if (window.streamCallbacks && window.streamCallbacks['%s']) { " +
                                    "window.streamCallbacks['%s'](%s); }",
                                    callbackId, callbackId, chunkResult.toString()
                                );
                                webView.evaluateJavascript(jsCallback, null);
                            } catch (JSONException e) {
                                Log.e(TAG, "Error creating chunk JSON", e);
                            }
                        });
                    }

                    // Send completion signal
                    runOnUiThread(() -> {
                        try {
                            JSONObject doneResult = new JSONObject();
                            doneResult.put("success", true);
                            doneResult.put("done", true);

                            String jsCallback = String.format(
                                "if (window.streamCallbacks && window.streamCallbacks['%s']) { " +
                                "window.streamCallbacks['%s'](%s); " +
                                "delete window.streamCallbacks['%s']; }",
                                callbackId, callbackId, doneResult.toString(), callbackId
                            );
                            webView.evaluateJavascript(jsCallback, null);
                        } catch (JSONException e) {
                            Log.e(TAG, "Error creating done JSON", e);
                        }
                    });
                }
            } else {
                // Handle error response
                StringBuilder errorResponse = new StringBuilder();
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getErrorStream(), "UTF-8"))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        errorResponse.append(line).append("\n");
                    }
                }

                throw new Exception("HTTP " + responseCode + ": " + errorResponse.toString().trim());
            }

        } finally {
            connection.disconnect();
        }
    }
}
