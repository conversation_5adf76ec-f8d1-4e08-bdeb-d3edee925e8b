1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.jermescode.ai"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- Internet permission for AI chat functionality -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:6:5-67
12-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:7:5-79
13-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:7:22-76
14
15    <!-- Camera permission for photo capture -->
16    <uses-permission android:name="android.permission.CAMERA" />
16-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:10:5-65
16-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:10:22-62
17
18    <uses-feature
18-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:11:5-85
19        android:name="android.hardware.camera"
19-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:11:19-57
20        android:required="false" />
20-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:11:58-82
21    <uses-feature
21-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:12:5-95
22        android:name="android.hardware.camera.autofocus"
22-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:12:19-67
23        android:required="false" />
23-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:12:68-92
24
25    <!-- Storage permissions for file uploads and chat history export/import -->
26    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
26-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:15:5-80
26-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:15:22-77
27    <uses-permission
27-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:16:5-17:38
28        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
28-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:16:22-78
29        android:maxSdkVersion="28" />
29-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:17:9-35
30    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
30-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:18:5-76
30-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:18:22-73
31    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
31-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:19:5-75
31-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:19:22-72
32    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
32-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:20:5-75
32-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:20:22-72
33
34    <!-- File provider for sharing files -->
35    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
35-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:23:5-24:40
35-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:23:22-79
36
37    <permission
37-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
38        android:name="com.jermescode.ai.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
38-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
39        android:protectionLevel="signature" />
39-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
40
41    <uses-permission android:name="com.jermescode.ai.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
41-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
41-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
42
43    <application
43-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:26:5-60:19
44        android:allowBackup="true"
44-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:27:9-35
45        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
45-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
46        android:dataExtractionRules="@xml/data_extraction_rules"
46-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:28:9-65
47        android:extractNativeLibs="false"
48        android:fullBackupContent="@xml/backup_rules"
48-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:29:9-54
49        android:icon="@mipmap/ic_launcher"
49-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:30:9-43
50        android:label="@string/app_name"
50-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:31:9-41
51        android:networkSecurityConfig="@xml/network_security_config"
51-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:35:9-69
52        android:roundIcon="@mipmap/ic_launcher_round"
52-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:32:9-54
53        android:theme="@style/Theme.JermesaCodeAi.Splash"
53-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:33:9-58
54        android:usesCleartextTraffic="true" >
54-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:34:9-44
55        <activity
55-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:38:9-48:20
56            android:name="com.jermescode.ai.MainActivity"
56-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:39:13-41
57            android:configChanges="orientation|screenSize|keyboardHidden"
57-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:43:13-74
58            android:exported="true"
58-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:40:13-36
59            android:screenOrientation="unspecified"
59-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:42:13-52
60            android:theme="@style/Theme.JermesaCodeAi.Splash" >
60-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:41:13-62
61            <intent-filter>
61-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:44:13-47:29
62                <action android:name="android.intent.action.MAIN" />
62-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:45:17-69
62-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:45:25-66
63
64                <category android:name="android.intent.category.LAUNCHER" />
64-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:46:17-77
64-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:46:27-74
65            </intent-filter>
66        </activity>
67
68        <!-- File provider for sharing files -->
69        <provider
70            android:name="androidx.core.content.FileProvider"
70-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:52:13-62
71            android:authorities="com.jermescode.ai.fileprovider"
71-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:53:13-64
72            android:exported="false"
72-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:54:13-37
73            android:grantUriPermissions="true" >
73-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:55:13-47
74            <meta-data
74-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:56:13-58:54
75                android:name="android.support.FILE_PROVIDER_PATHS"
75-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:57:17-67
76                android:resource="@xml/file_paths" />
76-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:58:17-51
77        </provider>
78        <provider
78-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\64e999c457a740eff668786494c619dc\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
79            android:name="androidx.startup.InitializationProvider"
79-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\64e999c457a740eff668786494c619dc\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
80            android:authorities="com.jermescode.ai.androidx-startup"
80-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\64e999c457a740eff668786494c619dc\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
81            android:exported="false" >
81-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\64e999c457a740eff668786494c619dc\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
82            <meta-data
82-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\64e999c457a740eff668786494c619dc\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
83                android:name="androidx.emoji2.text.EmojiCompatInitializer"
83-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\64e999c457a740eff668786494c619dc\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
84                android:value="androidx.startup" />
84-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\64e999c457a740eff668786494c619dc\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
85            <meta-data
85-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\002069901257091e303b084a0bc88eae\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
86                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
86-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\002069901257091e303b084a0bc88eae\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
87                android:value="androidx.startup" />
87-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\002069901257091e303b084a0bc88eae\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
88            <meta-data
88-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
89                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
89-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
90                android:value="androidx.startup" />
90-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
91        </provider>
92
93        <receiver
93-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
94            android:name="androidx.profileinstaller.ProfileInstallReceiver"
94-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
95            android:directBootAware="false"
95-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
96            android:enabled="true"
96-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
97            android:exported="true"
97-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
98            android:permission="android.permission.DUMP" >
98-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
99            <intent-filter>
99-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
100                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
100-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
100-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
101            </intent-filter>
102            <intent-filter>
102-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
103                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
103-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
103-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
104            </intent-filter>
105            <intent-filter>
105-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
106                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
106-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
106-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
107            </intent-filter>
108            <intent-filter>
108-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
109                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
109-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
109-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
110            </intent-filter>
111        </receiver>
112    </application>
113
114</manifest>
