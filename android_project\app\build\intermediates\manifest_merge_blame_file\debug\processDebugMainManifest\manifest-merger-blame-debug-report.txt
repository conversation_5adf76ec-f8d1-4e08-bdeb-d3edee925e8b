1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.jermescode.ai"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- Internet permission for AI chat functionality -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:6:5-67
12-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:7:5-79
13-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:7:22-76
14
15    <!-- Camera permission for photo capture -->
16    <uses-permission android:name="android.permission.CAMERA" />
16-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:10:5-65
16-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:10:22-62
17
18    <uses-feature
18-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:11:5-85
19        android:name="android.hardware.camera"
19-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:11:19-57
20        android:required="false" />
20-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:11:58-82
21    <uses-feature
21-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:12:5-95
22        android:name="android.hardware.camera.autofocus"
22-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:12:19-67
23        android:required="false" />
23-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:12:68-92
24
25    <!-- Storage permissions for file uploads and chat history export/import -->
26    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
26-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:15:5-80
26-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:15:22-77
27    <uses-permission
27-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:16:5-17:38
28        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
28-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:16:22-78
29        android:maxSdkVersion="28" />
29-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:17:9-35
30    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
30-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:18:5-76
30-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:18:22-73
31    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
31-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:19:5-75
31-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:19:22-72
32    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
32-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:20:5-75
32-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:20:22-72
33
34    <!-- File provider for sharing files -->
35    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
35-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:23:5-24:40
35-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:23:22-79
36
37    <permission
37-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
38        android:name="com.jermescode.ai.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
38-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
39        android:protectionLevel="signature" />
39-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
40
41    <uses-permission android:name="com.jermescode.ai.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
41-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
41-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
42
43    <application
43-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:26:5-59:19
44        android:allowBackup="true"
44-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:27:9-35
45        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
45-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
46        android:dataExtractionRules="@xml/data_extraction_rules"
46-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:28:9-65
47        android:debuggable="true"
48        android:extractNativeLibs="false"
49        android:fullBackupContent="@xml/backup_rules"
49-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:29:9-54
50        android:icon="@mipmap/ic_launcher"
50-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:30:9-43
51        android:label="@string/app_name"
51-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:31:9-41
52        android:roundIcon="@mipmap/ic_launcher_round"
52-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:32:9-54
53        android:testOnly="true"
54        android:theme="@style/Theme.JermesaCodeAi.Splash"
54-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:33:9-58
55        android:usesCleartextTraffic="true" >
55-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:34:9-44
56        <activity
56-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:37:9-47:20
57            android:name="com.jermescode.ai.MainActivity"
57-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:38:13-41
58            android:configChanges="orientation|screenSize|keyboardHidden"
58-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:42:13-74
59            android:exported="true"
59-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:39:13-36
60            android:screenOrientation="unspecified"
60-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:41:13-52
61            android:theme="@style/Theme.JermesaCodeAi.Splash" >
61-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:40:13-62
62            <intent-filter>
62-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:43:13-46:29
63                <action android:name="android.intent.action.MAIN" />
63-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:44:17-69
63-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:44:25-66
64
65                <category android:name="android.intent.category.LAUNCHER" />
65-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:45:17-77
65-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:45:27-74
66            </intent-filter>
67        </activity>
68
69        <!-- File provider for sharing files -->
70        <provider
71            android:name="androidx.core.content.FileProvider"
71-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:51:13-62
72            android:authorities="com.jermescode.ai.fileprovider"
72-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:52:13-64
73            android:exported="false"
73-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:53:13-37
74            android:grantUriPermissions="true" >
74-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:54:13-47
75            <meta-data
75-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:55:13-57:54
76                android:name="android.support.FILE_PROVIDER_PATHS"
76-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:56:17-67
77                android:resource="@xml/file_paths" />
77-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:57:17-51
78        </provider>
79        <provider
79-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\64e999c457a740eff668786494c619dc\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
80            android:name="androidx.startup.InitializationProvider"
80-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\64e999c457a740eff668786494c619dc\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
81            android:authorities="com.jermescode.ai.androidx-startup"
81-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\64e999c457a740eff668786494c619dc\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
82            android:exported="false" >
82-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\64e999c457a740eff668786494c619dc\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
83            <meta-data
83-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\64e999c457a740eff668786494c619dc\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
84                android:name="androidx.emoji2.text.EmojiCompatInitializer"
84-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\64e999c457a740eff668786494c619dc\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
85                android:value="androidx.startup" />
85-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\64e999c457a740eff668786494c619dc\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
86            <meta-data
86-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\002069901257091e303b084a0bc88eae\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
87                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
87-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\002069901257091e303b084a0bc88eae\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
88                android:value="androidx.startup" />
88-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\002069901257091e303b084a0bc88eae\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
89            <meta-data
89-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
90                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
90-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
91                android:value="androidx.startup" />
91-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
92        </provider>
93
94        <receiver
94-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
95            android:name="androidx.profileinstaller.ProfileInstallReceiver"
95-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
96            android:directBootAware="false"
96-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
97            android:enabled="true"
97-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
98            android:exported="true"
98-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
99            android:permission="android.permission.DUMP" >
99-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
100            <intent-filter>
100-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
101                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
101-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
101-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
102            </intent-filter>
103            <intent-filter>
103-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
104                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
104-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
104-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
105            </intent-filter>
106            <intent-filter>
106-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
107                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
107-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
107-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
108            </intent-filter>
109            <intent-filter>
109-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
110                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
110-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
110-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
111            </intent-filter>
112        </receiver>
113    </application>
114
115</manifest>
